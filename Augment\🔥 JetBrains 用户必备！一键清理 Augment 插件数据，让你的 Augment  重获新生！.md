<div data-theme-toc="true"> </div>

#🔥 JetBrains 用户必备！一键清理 Augment 插件数据，让你的 Augment  瘦身、清理聊天记录、API加速，重获新生！

## 前言
最近一直在折腾Augment和Cursor教育号，Augment的这个d*分流我感觉挺傻逼的，不会自动他只会在登录获取token和重启访问才有可能触发转移分流到其他域名而且还有可能延迟跟高，草，这个世界果然就是一个草台班子。

PS：我用了微信爆款标题，能爆火吗？
PS：这二天Cursor教育号难过，谁研究出新方案？能不能搭个伙合作一下。


## d1~d20 域名IP地址分析

**IP地址分析：**
* **网络范围**：所有IP均属于Google Cloud Platform
* **地理分布**：
  * `35.190.x.x` - GCP美国区域
  * `35.227.x.x` - GCP美国区域
  * `34.54.x.x` - GCP亚洲区域
  * `34.149.x.x` - GCP欧洲区域
  * `34.8.x.x` - GCP美国区域
  * `34.117.x.x` - GCP亚洲区域
  * `34.160.x.x` - GCP欧洲区域
### **流量分配机制**

**智能路由策略：**

* **客户端选择**：IDE插件通过配置或动态发现选择API端点
* **负载均衡**：多个 `d*` 域名支持并行处理
* **故障转移**：多域名设计提供高可用性保障

**分配策略分析：**

* **地理就近**：基于用户位置选择最近的GCP区域
* **负载均衡**：根据服务器负载动态分配请求
* **功能分离**：不同类型的请求路由到专门的服务集群

**实时处理能力：**

* 支持"数千文件/秒"的处理能力
* 实现"几秒内"的索引更新
* 支持大规模并发用户访问


## 软件介绍

### 核心功能一览 ✨

**🎯 全系列 JetBrains 产品支持**
- IntelliJ IDEA、PyCharm、WebStorm、PhpStorm
- RubyMine、CLion、DataGrip、GoLand、Rider
- Android Studio...几乎覆盖所有你能想到的 JetBrains IDE！

**🧹 深度清理能力**

- 配置文件清理：彻底移除 XML 配置中的 Augment 属性
- 缓存文件清理：清空所有临时数据和日志文件
- 项目数据清理：扫描项目目录，清理 `.idea` 文件夹中的残留
- Windows 注册表清理：深入系统底层，不留死角
- AppData 清理：清理用户数据目录中的相关文件
- 用户目录清理：移除 `.augmentcode` 和 `.jetbrains` 目录

**🌍 跨平台兼容**

- Windows：完美支持，包括注册表清理
- macOS：适配 Library 目录结构
- Linux：支持标准 Unix 目录布局

**⚡ 智能化特性**

- Dry-run 模式：预览将要删除的文件，安全第一
- 详细日志：每个操作都有清晰记录
- 进程检测：自动检查 IDE 是否在运行
- 备份机制：重要操作前自动备份

- 交互式配置：根据需求定制清理范围

### 🌟 独家黑科技：API 域名优化功能


**智能测速系统**

- 自动测试 d1~d20.api.augmentcode.com 所有域名
- 并发测试，5秒内完成全部检测
- 三次测试取平均值，确保结果准确

**代理支持**

- Clash 代理 (127.0.0.1:7890)
- V2Ray 代理 (127.0.0.1:1080)
- 自定义代理配置
- 智能网络环境检测

**Hosts 文件优化**

- 自动选择最快的5个域名
- 智能更新系统 hosts 文件
- 自动备份原始文件
- 跨平台权限处理

## 🎛️ 命令行参数详解

| 参数 | 说明 | 使用场景 |
|------|------|----------|
| `-h, --help` | 显示帮助信息 | 新手必看 |
| `-v, --verbose` | 详细输出模式 | 想看清理过程 |
| `-d, --dry-run` | 预览模式 | 安全第一，先看看 |
| `-s, --silent` | 静默模式 | 自动化脚本 |
| `--unsafe` | 非安全模式 | 更彻底清理 |
| `--ide=NAME` | 指定 IDE | 只清理某个产品 |
| `--chat-only` | 只清理聊天 | 保留其他设置 |
| `--keep-settings` | 保留设置 | 只清理数据 |

## ⚠️ 重要注意事项

### 使用前必读 📋

**1. 关闭所有 JetBrains IDE**

- 工具会自动检测运行中的进程
- 如果检测到 IDE 在运行，会提示关闭

**2. 退出 Augment 账号登录**

- 在所有 IDE 中退出 Augment 登录
- 确保数据同步完成

**3. 备份重要数据**

- 工具会自动备份 hosts 文件
- 建议手动备份重要的项目配置

**4. 管理员权限（Windows）**

- 清理注册表需要管理员权限
- 修改 hosts 文件需要管理员权限

### 清理后的效果 ✨

**立竿见影的改善：**

- 🚀 IDE 启动速度提升 50%+
- 💾 内存占用减少 200MB+
- 🔄 Augment 插件恢复全新状态
- 🧹 项目配置文件更加干净
- ⚡ API 访问速度显著提升


## 清理工具下载⏬

[Releases · yuaotian/go-augment-cleaner](https://github.com/yuaotian/go-augment-cleaner/releases)


### Windows 系统使用方法 🪟

- 右键点击 `augment-cleaner.exe`
- 选择"以管理员身份运行"
- 这样才能清理注册表和 hosts 文件

### macOS/Linux 系统使用方法 🍎/🐧

```bash

# 给予执行权限
chmod +x augment-cleaner

# 基础清理
./augment-cleaner

# 需要 sudo 权限来修改 hosts 文件
sudo ./augment-cleaner

```

**macOS 特殊说明：**
- 清理 `~/Library/Application Support/JetBrains`
- 清理 `~/Library/Caches/JetBrains`
- 清理 `~/Library/Preferences/JetBrains`



#develop 