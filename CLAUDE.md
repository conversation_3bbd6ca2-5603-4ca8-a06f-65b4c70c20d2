# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a Chinese-language technical blog and documentation repository focused on AI development tools, programming assistants, and system troubleshooting. The repository contains articles, tutorials, and guides about various AI coding tools like Cursor, Augment, Claude Code, and Gemini CLI.

## Repository Structure

### Main Content Categories

- **AI测评/** - AI model evaluations and comparisons
- **Augment/** - Augment AI editor tutorials, troubleshooting, and optimization guides
- **Cursor/** - Cursor IDE reviews, pricing analysis, and issue resolution
- **Windos/** - Windows system troubleshooting and WSL-related fixes
- **gemini-cli/** - Gemini CLI installation and usage guides
- **提示词/** - Prompt engineering and AI assistant optimization techniques

### Content Characteristics

- All content is in Chinese with technical terminology
- Articles focus on practical usage, troubleshooting, and optimization
- Heavy emphasis on AI coding tools comparison and evaluation
- Contains PowerShell scripts and technical solutions
- Includes screenshots and visual references in `img/` subdirectories

## Content Themes

### AI Development Tools Coverage
- **Cursor**: Pricing analysis, usage limits, feature comparisons
- **Augment**: ACE context engine, prompt optimization, troubleshooting
- **Claude Code**: Usage patterns and capabilities
- **Gemini CLI**: Installation guides and usage patterns

### Technical Content Types
- Tool comparisons and recommendations
- Troubleshooting guides and solutions
- PowerShell automation scripts
- System configuration and optimization
- Prompt engineering techniques

## File Naming Conventions

- Main articles use descriptive Chinese titles with `.md` extension
- Image files use mixed naming (PixPin_, Pasted image_, etc.)
- Some files contain special characters and Chinese punctuation
- Directory names are primarily in Chinese

## Technical Notes

- The repository uses Obsidian-style wiki links `[[image.png]]` for image references
- PowerShell scripts are embedded within markdown files
- Content focuses on practical, actionable solutions for developers
- Regular updates reflect changing AI tool landscapes and pricing models

## Working with This Repository

### Content Creation
- Maintain Chinese language consistency
- Follow existing article structure and formatting
- Include practical examples and troubleshooting steps
- Add screenshots in appropriate `img/` subdirectories

### Technical Articles
- Focus on AI development tools and their practical applications
- Include comparison tables and evaluation criteria
- Provide step-by-step solutions for common issues
- Reference related articles within the repository

### Code and Scripts
- PowerShell scripts should be well-commented in Chinese
- Include error handling and logging where appropriate
- Test scripts on Windows environments
- Document prerequisites and requirements