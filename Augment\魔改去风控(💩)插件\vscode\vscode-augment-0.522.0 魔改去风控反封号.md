## 前言

>扒开源码什么妖魔鬼怪都要显出来原型，下面是我搞崩了几个Augment号、花了差不多一个亿GLM4.5分析逆向出来的魔改去风控反封号版本。

![[Pasted image 20250806102625.png]]

---

# 🚀 Augment 魔改去风控插件 - 功能详解

## 📋 插件概述

这是一个专为 VSCode 开发环境设计的**网络请求拦截与隐私保护插件**，主要功能是拦截和修改特定的网络请求，替换敏感的会话ID和系统信息，为开发者提供隐私保护和请求管理功能。

## ✨ 核心功能特性

### 🌐 **全方位网络请求拦截**
- **HTTP/HTTPS 模块拦截**：深度拦截 Node.js 原生网络模块
- **Fetch API 拦截**：全局 fetch 请求监控和修改
- **Axios 拦截器**：专门针对 Axios 库的请求拦截
- **XMLHttpRequest 拦截**：传统 AJAX 请求的完整控制

### 🔐 **智能会话ID替换**
- **UUID v4 格式检测**：自动识别标准 UUID 格式的会话ID
- **32位十六进制检测**：支持各种格式的会话标识符
- **关键词匹配**：智能识别包含"session"关键词的标识符
- **伪造ID生成**：生成符合标准的伪造会话ID进行替换

### 🖥️ **系统信息伪造**
- **macOS 系统伪造**：
  - IOPlatformUUID 替换
  - IOPlatformSerialNumber 伪造
  - board-id 信息修改
- **Windows 系统伪造**：
  - MachineGuid 替换
  - ProductId 伪造
  - SerialNumber 修改

### 🛠️ **命令输出拦截**
- **ioreg 命令拦截**：macOS 系统信息查询命令
- **注册表命令拦截**：Windows 系统注册表查询
- **Git 命令拦截**：版本控制相关命令输出
- **系统信息命令**：wmic、systeminfo 等系统查询命令

## 🎯 **使用场景与适用范围**

### 👨‍💻 **开发者隐私保护**
- 在使用各种开发工具和服务时保护个人设备信息
- 防止敏感的硬件标识符被第三方服务收集
- 为开发环境提供额外的隐私保护层

### 🔧 **开发环境管理**
- 统一管理开发过程中的网络请求
- 控制和监控第三方服务的数据收集行为
- 提供开发调试时的请求拦截功能

### 🛡️ **企业级安全**
- 企业开发环境中的敏感信息保护
- 防止开发工具泄露企业设备信息
- 提供可控的网络请求管理机制

## 💡 **技术亮点与创新**

### 🧠 **智能时间验证机制**
- **多重验证算法**：采用编码日期、固定日期、时间戳等多种验证方式
- **二进制编码**：使用二进制编码技术隐藏关键验证逻辑
- **双重验证模块**：网络拦截和系统伪造分别使用独立的验证机制

### 🔄 **深度模块拦截**
- **require 函数重写**：在模块加载层面进行拦截
- **原型链修改**：直接修改 JavaScript 原生对象的原型
- **全局状态管理**：维护插件的全局运行状态和配置

### 🎨 **用户体验优化**
- **VSCode 深度集成**：注册自定义命令和菜单项
- **可视化信息展示**：提供友好的信息对话框
- **一键复制功能**：方便用户获取技术支持信息

## 📱 **安装与使用**

### 🚀 **自动激活**
插件会在 VSCode 启动时自动激活，无需手动配置。所有拦截功能会在后台静默运行，为您的开发环境提供持续的隐私保护。



## 结束

> idea版本还在弄，马上就好了，到时候搞完给大家试试看。

## 下载⏬地址(蓝奏)

```
https://wwi.lanzouo.com/b00od7abwj  
密码:8686

```




