# 【Augment】VSCode&JetBrains 用户必看 3 步解决彻底解决 Augment 登录失败问题：Sign in failed. If you have a firewall, please add "https://i1.api.augmentcode.com/" to your allowlist.


## 前言

论坛和群里小伙伴一直问为什么免费的free套餐无法登陆插件，下午我换号才突然意识到，好像Augment专门在这个`i1`接口上锁国区，不知道什么什么时候开始的，迷迷糊糊在群里是上星期就开始了？！大家可以手动ping或者curl试试看就知道了，你会发现用curl挂上代理就能访问到了，又一个锁国区了，唉~ :bili_001:

![[Pasted image 20250731184709.png]]

PS：**代理规则必须要走`全局`**

## 手动测试

### 挂代理
```bash
curl -v -x http:/127.0.0.1:7890 https://i1.api.augmentcode.com
```
![[Pasted image 20250731184751.png]]

### 不挂代理无法访问

```bash
curl -v  https://i1.api.augmentcode.com
```

![[Pasted image 20250731184806.png]]

## Cursor和VSCode 代理

### 代理设置（json）

> 快捷键：ctrl+shift+p，输入 user json 出现下面👇直接打开复制即可，
> ![[Pasted image 20250731184815.png]]
> ![[Pasted image 20250731184821.png]]
#### json 
> 默认clash端口为准，其他的端口请以自己的番茄🍅软件为准！
```BASH
    "http.proxy": "http://127.0.0.1:7890", //代理
    "http.proxyAuthorization": null,  //为每个网络请求的 “Proxy-Authorization” 标头发送的值，类似于代理的验证，默认null就行，除非你的代理有验证账号或者token
    "http.experimental.systemCertificatesV2": true, //控制是否应启用从 OS 实验性加载 CA 证书。这使用的方法比默认实现更常见。在remote development禁用#http.useLocalProxyConfiguration#设置时，可在本地设置和远程设置中单独配置此设置。
    "http.useLocalProxyConfiguration":true, //控制是否在远程扩展主机中使用本地代理配置。此设置仅在 remote development. 期间用作远程设置
    "http.proxyStrictSSL": false, //控制是否应根据提供的 CA 列表验证代理服务器证书。在 remote development 禁用 #http.useLocalProxyConfiguration# 设置时，可在本地设置和远程设置中单独配置此设置
    "http.proxySupport": "on",//对扩展使用代理支持。在 remote development 禁用 #http.useLocalProxyConfiguration# 设置时，可在本地设置和远程设置中单独配置此设置。

```


## jeb系列的ide编辑器代理

> 打开左上角：文件 -> 设置 -> 外观与行为 -> HTTP代理 
> 输入自己的当前番茄代理端口，然后点击测试 `检查连接` 即可

![[Pasted image 20250731184910.png]]


> 连接测试

![[Pasted image 20250731184926.png]]
## 番茄🍅设置，verge为例
### verge 设置
![[Pasted image 20250731184938.png]]
### 规则改成全局
![[Pasted image 20250731184952.png]]

## 结束 🔚

说白了，其实就是Augment的 `i1` 接口锁国区导致的，真特喵的无语😅

### 重要事情说三百遍：
PS：**代理规则必须要走 `全局`**
PS：**代理规则必须要走 `全局`**
PS：**代理规则必须要走 `全局`**

