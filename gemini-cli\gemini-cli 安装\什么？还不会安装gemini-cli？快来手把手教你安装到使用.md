### 1、安装 Gemini-Cli
>  为了方便，下面 Gemini-Cli 统称为 `gc` 
>  Node 版本最低 18+以上，请先安装Node~

```bash

# 全局安装 CLI
npm install -g @google/gemini-cli

# 现在您可以从任何地方运行 CLI
gemini

```

### 2、登录
#### 前置条件（番茄🍅）

##### 设置代理

> 需要根据你自己的番茄软件来设置，终端代理

```bash
set HTTP_PROXY=http://127.0.0.1:7890/
set HTTPS_PROXY=http://127.0.0.1:7890/
```

##### （推荐）设置全局Tun模式（Clash）

> 如果你的番茄🍅可以全局Tun，那么就直接全局Tun模式即可

![[PixPin_2025-06-26_16-33-49.png]]

![[Pasted image **************.png]]

> 只要你能在终端 `ping google.com` ping 通谷歌域名3即可

![[PixPin_2025-06-26_16-36-22.png]]


#### 输入 `gemini` 即可进入 `gemini-cli`  终端 开始登录

> 输入 gemini 进入终端
![[PixPin_2025-06-26_16-24-06.png]]

> 登录Google  选择账号
![[PixPin_2025-06-26_16-37-24.png]]

> 点击登录 
![[PixPin_2025-06-26_16-37-46.png]]

> 说明登录成功
![[PixPin_2025-06-26_16-38-05.png]]

##### 登录成功，但是终端异常 Failed to login. Workspace accounts 


> 如果 gemini 终端出现下面提示：
> Failed to login. Workspace accounts and licensed Code Assist users must configure GOOGLE_CLOUD_PROJECT   
 (see https://goo.gle/gemini-cli-auth-docs#workspace-gca).                                                │
 Message: Request contains an invalid argument.
![[PixPin_2025-06-26_16-39-31.png]]


##### 进入 谷歌的 aistudio 获取 项目ID（PROJECT）

> [Google AI Studio: ](https://aistudio.google.com/) https://aistudio.google.com
![[PixPin_2025-06-26_16-42-15.png]]


> 点击 【项目编号】
![[PixPin_2025-06-26_16-42-32.png]]

> 弹窗点击复制纯数字的项目编号【599***】
![[PixPin_2025-06-26_16-42-46.png]]

#####  启用 Project ID（项目ID）

> [Google云端平台]() : https://console.developers.google.com/apis/api/cloudaicompanion.googleapis.com/overview?project=
启用状态
![[PixPin_2025-06-26_16-56-47.png]]


##### 进入 .gemini 设置 env 环境变量

> 进入下面这个window文件夹
Windows：
```c
%USERPROFILE%/.gemini
```

>  进入指定文件夹，新建一个.env 环境变量文件，然后编辑写入 Project ID 

```bash
// Project ID 替换成你自己的那个项目ID！！！
export GOOGLE_CLOUD_PROJECT= Project ID
```
![[PixPin_2025-06-26_16-45-45.png]]


#### 重启终端并且重新进入gemini

> 如果你设置的是代理，需要重新设置，如果你是tun模式请不要关闭

> 如果你进入的Gemini是下面这样的就说明成功了~
![[PixPin_2025-06-26_16-47-51.png]]



### 3、设置 （settings.json）

 > emini CLI 使用 `settings.json` 文件进行持久配置。

这些文件有两个位置：
- **用户设置文件：**
    - **位置：** `~/.gemini/settings.json`（其中 `~` 是您的主目录）。
    - **作用域：** 适用于当前用户的所有 Gemini CLI 会话。
- **项目设置文件：**
    - **位置：** 项目根目录内的 `.gemini/settings.json`。
    - **作用域：** 仅在从特定项目运行 Gemini CLI 时适用。项目设置会覆盖用户设置。

**设置中环境变量的注意事项：** 您的 `settings.json` 文件中的字符串值可以使用 `$VAR_NAME` 或 `${VAR_NAME}` 语法引用环境变量。加载设置时，这些变量将自动解析。例如，如果您有环境变量 `MY_API_TOKEN`，可以在 `settings.json` 中这样使用：`"apiKey": "$MY_API_TOKEN"`。

#### 配置MCP服务器

> 在 `settings.json` 中配置 MCP 服务器，在项目的根目录中，创建或打开`.gemini/settings.json` 。在文件中，添加 `mcpServers` 配置块

示例：
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": [
        "mcp-feedback-enhanced@latest"
      ],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false"
      },
      "autoApprove": [
        "interactive_feedback"
      ]
    }
  }
}
```




### 4、使用技巧

> 记住，所有交互都需要番茄🍅（代理）的情况下，所以不要断开TUN模式

#### 非交互模式
```bash
echo "煎饼果子好吃吗😋？" | gemini
```

```bash
gemini -p "煎饼果子好吃吗😋？"
```


##### Curosr、vscode 打开终端 输入 `gemini ` 

> Curosr、vscode 打开终端 输入 `gemini ` 默认进入当前项目文件夹

> 输入：检查我这个这个项目是做什么的？
> 默认检查  `README.md` 文件好评，但是他没有Cursor和Augment的项目结构初始化和代码所以向量化，只能一个一个文件找，有一点...鸡肋吧，希望继续加油~
![[PixPin_2025-06-26_23-19-53.png]]

