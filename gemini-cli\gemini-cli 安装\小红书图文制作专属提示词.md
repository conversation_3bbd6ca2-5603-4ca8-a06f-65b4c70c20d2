# 小红书技术教程图文制作专属提示词

## 🎯 核心任务定义
基于技术文档创建小红书风格的HTML图文教程，适合逐步截图发布到小红书平台。

## 📋 制作规范

### 🎨 视觉设计规范
- **主色调**：小红书经典粉色渐变 (#ff9a9e 到 #fecfef)
- **页面宽度**：1020px（宽屏版），适合桌面和平板截图
- **布局方式**：双栏网格布局，充分利用空间
- **字体大小**：标题32px，正文15px，代码14px
- **圆角设计**：统一使用15-20px圆角，营造温馨感
- **阴影效果**：多层次阴影增强立体感

### 📱 小红书适配要求
- **分页策略**：每个功能模块一页，便于单独截图,图片要完整，但是大小最好整齐排列等比例缩放且符合宽屏，不要太大，不要太小，要不然截图不好看
- **步骤细化**：复杂操作拆分成多个简单步骤
- **emoji装饰**：每个章节配对应表情符号
- **交互提示**：添加截图发布建议
- **内容密度**：每页聚焦单一主题，避免信息过载


## 📝 内容组织策略

### 🏗️ 页面结构设计
1. **封面页**：吸引眼球的标题 + 学习目标 + 准备清单
2. **功能页**：每个主要功能独占一页
3. **问题页**：常见错误和解决方案
4. **配置页**：详细的设置步骤
5. **技巧页**：高级用法和最佳实践
6. **总结页**：完整操作清单 + 重要链接

### 📷 图片集成原则
- **截图完整性**：原文档中的所有图片必须包含
- **图片说明**：每张图片配详细的步骤说明
- **布局优化**：使用网格布局展示多张相关图片
- **视觉层次**：重要截图突出显示

### 🔗 链接处理规范
- **完整展示**：链接标题和完整URL都要显示
- **格式统一**：链接 + URL展示框的组合方式
- **敏感词处理**：网络、翻墙等词汇用同音字替代
- **便民设计**：重要链接集中在总结页

## ⚠️ 敏感词处理策略

### 🔄 替换对照表
- 网络 → 王络
- 翻墙 → 翻強
- 代理 → 代理（保留，技术术语）
- 科学上网 → 科学上网（保留，委婉表达）
- VPN → VPN（保留，技术术语）

### 📝 表达技巧
- 使用技术性描述而非政治性词汇
- 采用委婉和专业的表达方式
- 重点强调技术教学属性

## 🎯 质量检查清单

### ✅ 内容完整性
- [ ] 原文档所有步骤已转化
- [ ] 所有图片已正确引用
- [ ] 所有链接已完整展示
- [ ] 代码块格式正确

### ✅ 视觉效果
- [ ] 小红书风格色彩一致
- [ ] 页面布局平衡美观
- [ ] 图片展示清晰规整
- [ ] 交互效果流畅

### ✅ 用户体验
- [ ] 导航功能正常
- [ ] 代码复制功能可用
- [ ] 页面切换顺畅
- [ ] 移动端适配良好

### ✅ 发布适配
- [ ] 每页内容适合单独截图
- [ ] 步骤分解合理
- [ ] 截图提示明确
- [ ] 敏感词已处理

## 🚀 输出规范

### 📁 文件命名
- 主文件：`小红书图文_{主题}_宽屏版.html`
- 辅助文件：原图片文件夹保持原有结构

### 📊 页面数量
- 建议6-10页，每页聚焦单一主题
- 复杂教程可适当增加页面数量
- 确保每页内容适中，便于截图

### 🔧 技术要求
- 使用纯HTML+CSS+JS，无外部依赖
- 确保在主流浏览器中正常显示
- 代码结构清晰，便于后续修改

## 💡 创新建议

### 🎨 视觉增强
- 考虑添加SVG图标装饰
- 使用更丰富的渐变效果
- 增加微交互动画

### 📱 功能扩展
- 添加进度指示器
- 增加页面跳转功能
- 考虑添加打印样式

### 🌟 内容优化
- 添加视频教程链接
- 增加常见问题FAQ
- 提供相关工具推荐

---

## 🎯 使用说明

当需要将技术文档转换为小红书图文时，请：

1. **分析原文档**：理解内容结构和关键步骤
2. **规划页面**：根据内容复杂度设计页面数量
3. **应用模板**：使用本提示词中的设计规范
4. **细化步骤**：确保每个操作都有详细说明
5. **集成图片**：正确引用所有原文档图片
6. **处理敏感词**：按照替换策略修改措辞
7. **质量检查**：使用检查清单验证完整性
8. **优化体验**：确保截图和发布的便利性

遵循此提示词，可以高效制作出专业、美观、适合小红书平台的技术教程图文内容。 