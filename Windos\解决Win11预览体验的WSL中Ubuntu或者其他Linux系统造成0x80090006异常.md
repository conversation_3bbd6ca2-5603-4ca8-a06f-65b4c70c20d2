## 背景

突然无缘无故启动失败，报什么错误代码 `0x80090006`，查了一下就是无效签名和证书过期，后面实在没办法，想到下面👇二个解决方案，但是都是临时的，我建议还是需要重装到`正式版` 不要尝鲜搞什么预览体验的`dev`、`Canary`、`bate` 版本= = 。

**背景信息：** 
- 系统：Windows 11  Dev 专业版 ，Microsoft Windows 版本 24H2 (OS 内部版本 26257.5000)
- 问题：WSL Ubuntu 启动失败，错误代码 `0x80090006`（无效签名，证书过期）  

启动报错
```bash
wsl
无效签名。
错误代码: Wsl/Service/CreateInstance/CreateVm/HCS/0x80090006
```

![[PixPin_2025-07-12_00-12-38.png]]



### 手动解决方案

> 临时解决方案：手动将系统时间调整到 `2024-08-06` ，在 `PowerShell ` 就能成功启动 **WSL**

> 请注意⚠️这个`2024-08-06`时间不是固定，具体你要看你自己的过期时间
> 快捷键：win+r   -  输入: winver
![[PixPin_2025-07-12_00-14-26.png]]

> 关闭自动设置时区
> 关闭自动设置时间
> 点击 `更改`
> 调整到你过期之前一般，调整 **年**、**月** 即可
![[PixPin_2025-07-12_00-16-50.png]]



## 自动调整（临时方案）

> 每次手动实在不方便，后面突然想到可以利用 PowerShell 7 配置文件 (`Microsoft.PowerShell_profile.ps1`) 中添加一个自动化解决方案来处理 WSL Ubuntu 启动时的证书过期问题，我是天才~核心解决方案就是异步检测+调整时间⌛️启动成功就调整回去~


```bash
# WSL 证书过期问题自动修复模块 - 完全异步版本

function Start-WSLAsyncCheck {
    param(
        [string]$LogPath = "$env:TEMP\wsl-cert-fix.log"
    )

    # 完全异步执行 WSL 检测和修复
    $job = Start-Job -ScriptBlock {
        param($LogPath)

        function Write-FixLog {
            param($Message, $Level = "INFO")
            $ts = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            Add-Content -Path $LogPath -Value "[$ts] [$Level] $Message"
        }

        function Test-WSLInstance {

            try {
                # 尝试启动 WSL 实例并检查是否出现证书错误
                $result = wsl --exec echo "test" 2>&1
                $hasCertError = $result -match "0x80090006" -or $result -match "无效签名" -or $result -match "invalid signature"
                if ($hasCertError) {
                    Write-FixLog "检测到 WSL 证书错误: $result" "ERROR"
                    return $false
                }

  
                if ($LASTEXITCODE -eq 0) {
                    Write-FixLog "WSL 实例运行正常" "SUCCESS"
                    return $true
                } else {
                    Write-FixLog "WSL 实例启动失败: $result" "WARN"
                    return $false
                }
            }
            catch {
                Write-FixLog "WSL 状态检测异常: $($_.Exception.Message)" "ERROR"
                return $false
            }
        }

 
        # 检查 WSL 状态
        Write-FixLog "开始 WSL 状态检查"
        if (Test-WSLInstance) {
            Write-FixLog "WSL 运行正常，无需修复" "SUCCESS"
            return $true
        }

        Write-FixLog "检测到 WSL 问题，开始修复流程" "WARN"


        try {
            # 检查是否需要管理员权限
            $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")


            if (-not $isAdmin) {
                Write-FixLog "需要管理员权限来修改系统时间，跳过修复" "ERROR"
                return $false
            }


            # 保存当前时间
            $originalTime = Get-Date
            Write-FixLog "保存当前时间: $originalTime"

            # 设置临时时间到 2024-08-06
            $tempDate = Get-Date "2024-08-06 12:00:00"
            Set-Date $tempDate
            Write-FixLog "临时设置系统时间为: $tempDate"

  
            # 等待系统时间生效
            Start-Sleep -Seconds 2

            # 尝试启动 WSL 实例
            Write-FixLog "尝试启动 WSL 实例..."
            $wslResult = wsl --exec echo "WSL修复测试" 2>&1

            # 检查是否还有证书错误
            $hasCertError = $wslResult -match "0x80090006" -or $wslResult -match "无效签名" -or $wslResult -match "invalid signature"

  
            if ($LASTEXITCODE -eq 0 -and -not $hasCertError) {
                Write-FixLog "WSL 实例启动成功，证书问题已解决" "SUCCESS"

                # 恢复系统时间
                Set-Date $originalTime
                Write-FixLog "系统时间已恢复为: $originalTime"
  

                # 同步网络时间
                try {
                    w32tm /resync /nowait | Out-Null
                    Write-FixLog "已触发网络时间同步"
                }
                catch {
                    Write-FixLog "网络时间同步失败: $($_.Exception.Message)" "WARN"
                }  
                return $true
            }
            else {
                Write-FixLog "WSL 实例启动失败或仍有证书错误: $wslResult" "ERROR"
                # 恢复时间
                Set-Date $originalTime
                Write-FixLog "系统时间已恢复"
                return $false
            }
        }
        catch {
            Write-FixLog "修复过程出错: $($_.Exception.Message)" "ERROR"
            try {
                # 确保时间恢复
                if ($originalTime) {
                    Set-Date $originalTime
                    Write-FixLog "紧急恢复系统时间"
                }
            }
            catch {
                Write-FixLog "紧急时间恢复失败: $($_.Exception.Message)" "ERROR"
            }
            return $false
        }
    } -ArgumentList $LogPath
  

    # 静默注册作业完成事件（不输出到控制台）
    Register-ObjectEvent -InputObject $job -EventName StateChanged -Action {
        $job = $Event.Sender
        if ($job.State -eq 'Completed') {
            $result = Receive-Job -Job $job
            # 静默处理，不输出到控制台
            Remove-Job -Job $job
            Unregister-Event -SourceIdentifier $Event.SourceIdentifier
        }
    } | Out-Null
  
    return $job
}

  
# 创建 WSL 管理别名和函数
function wsl-fix { Start-WSLWithCertFix }
function wsl-status {
    if (Test-WSLInstance) {
        Write-Host "✅ WSL 实例运行正常" -ForegroundColor Green
    } else {
        Write-Host "❌ WSL 实例存在证书问题或无法启动" -ForegroundColor Red
        Write-Host "   尝试运行 wsl-fix 进行修复" -ForegroundColor Yellow
    }
}

function wsl-log {
    $logPath = "$env:TEMP\wsl-cert-fix.log"
    if (Test-Path $logPath) {
        Get-Content $logPath -Tail 20
    } else {
        Write-Host "日志文件不存在" -ForegroundColor Yellow
    }
}

# PowerShell 启动时执行快速检测
Start-WSLAsyncCheck | Out-Null


```