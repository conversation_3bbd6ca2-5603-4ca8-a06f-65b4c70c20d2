## 前言

当前技巧来自，LinuxDo 论坛 @[WJune](https://linux.do/u/WJune) 非常感谢🙏佬的乐于分享~我下面的教程只是我个人测试有效，相关截图都是一步一步来的。

## 准备工具（邮箱、节点）

### 邮箱

微软（`微软`、`163`、`qq`、`雅虎`、`自建邮箱`等），不要用临时邮箱比如 **2925**、**tmpmail**等！augment会在网页直接无法登录！

### 节点
- 准备干净一点的 `节点`，最次都是**轻微**起步
![[ip.png]]

---
## 教程开始

### 第一步、注册 Augment

> 注册过程跳过，看第二步

### 第二步、生成 Cursor 付费 账单截图PDF

> 再次感谢🙏 来自 LinuxDo 的 开发 @[Sirhexs](https://linux.do/u/sirhexs)

![[PixPin_2025-07-27_17-06-20.png]]
![[PixPin_2025-07-27_17-07-10.png]]

#### 附我自己用，大家可以自己想办法转成pdf，比如wps就导出pdf一页，简简单单~

![[PixPin_2025-07-27_17-08-47.png]]

> 保存PDF，下一步要用，

### 第三步、上传PDF获取活动600次数

>  打开活动页面： www.augmentcode.com/resources/cursor

![[PixPin_2025-07-27_17-11-39.png]]


#### 进入当前页面

> 提醒，很多人在这一步挂掉了，就是节点不干净，请自己多切换新的节点，另外上传超过五次，还不行就会失去`活动资格`，换个号继续。

![[PixPin_2025-07-27_17-12-45.png]]

#### 只要上传⏫成功，基本就能直接获取600次数！
![[PixPin_2025-07-27_17-14-29.png]]


### 第四步、切换到免费Free套餐，可无视风控、封号使用一个月

> 请注意，这一步是最重要的，但也是你们需要考虑的，人家明确说明，切换到free套餐会拿你们的项目上传后台进行训练，这一点其实 **仁者见仁** 了，难道不是free套餐其他套餐就不会了嘛~所以大家考虑清楚。

#### 点击升级
![[PixPin_2025-07-27_17-18-04.png]]




#### 切换到Free

> 再一次警告⚠️，会上传项目代码进行内部训练，建议的请勿切换

> 再一次警告⚠️，会上传项目代码进行内部训练，建议的请勿切换

> 再一次警告⚠️，会上传项目代码进行内部训练，建议的请勿切换

![[PixPin_2025-07-27_17-18-28.png]]

#### 切换成功之后，你的次数应该是 650

>Cursor的活动拉人次数+Free免费次数： 600+50  = 650 ，过期时间就是一个月

![[PixPin_2025-07-27_17-20-58.png]]



## 结束

简单来说，就是利用Augment拉人活动送的600次数和免费账号可以无视风控、封号，直接畅玩！

Augment这一招釜底抽薪真的狠，打起来，打起来 😏



