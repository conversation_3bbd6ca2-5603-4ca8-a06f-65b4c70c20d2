<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书图文 | Gemini-CLI安装完整教程</title>
    <style>
        /* 小红书风格基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1020px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(255, 154, 158, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff9a9e, #fad0c4);
            padding: 30px;
            text-align: center;
            color: white;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .nav-bar {
            background: #fff;
            padding: 15px 30px;
            border-bottom: 2px solid #fecfef;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(135deg, #ff9a9e, #fad0c4);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 154, 158, 0.3);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
            transform: scale(1.05);
        }

        .content {
            padding: 30px;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 30px rgba(255, 154, 158, 0.15);
            border: 2px solid #fecfef;
        }

        .card h2 {
            color: #ff6b6b;
            font-size: 24px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card h3 {
            color: #ff8e8e;
            font-size: 18px;
            margin: 20px 0 10px 0;
        }

        .card p, .card li {
            color: #333;
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            overflow-x: auto;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ff9a9e;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .image-container {
            text-align: center;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border: 2px dashed #fecfef;
        }

        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .tip {
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            border: none;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #ff9a9e;
        }

        .warning {
            background: linear-gradient(135deg, #ffe8e8, #ffd6d6);
            border: none;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #ff6b6b;
        }

        .screenshot-hint {
            background: linear-gradient(135deg, #e8f5e8, #d4f8d4);
            border-radius: 10px;
            padding: 10px;
            margin: 20px 0;
            text-align: center;
            border: 2px dashed #4ade80;
            color: #166534;
            font-size: 14px;
        }

        .footer {
            background: linear-gradient(135deg, #ff9a9e, #fad0c4);
            padding: 20px;
            text-align: center;
            color: white;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
            .nav-bar {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 Gemini-CLI 完整安装教程</h1>
            <p>小红书技术教程 | 手把手教你从零开始 | 收藏不迷路</p>
        </div>

        <nav class="nav-bar">
            <button class="nav-btn active" onclick="showPage(0)">🏠 封面</button>
            <button class="nav-btn" onclick="showPage(1)">⚙️ 环境准备</button>
            <button class="nav-btn" onclick="showPage(2)">📦 安装步骤</button>
            <button class="nav-btn" onclick="showPage(3)">🔐 登录流程</button>
            <button class="nav-btn" onclick="showPage(4)">🔧 问题解决</button>
            <button class="nav-btn" onclick="showPage(5)">📋 配置设置</button>
            <button class="nav-btn" onclick="showPage(6)">💡 使用技巧</button>
            <button class="nav-btn" onclick="showPage(7)">📚 总结收藏</button>
        </nav>

        <div class="content">
            <!-- 第1页：封面页 -->
            <div class="page active" id="page-0">
                <div class="two-column">
                    <div class="card">
                        <h2>🎯 学习目标</h2>
                        <ul>
                            <li>✅ 完成 Gemini-CLI 全局安装</li>
                            <li>✅ 成功登录 Google 账号</li>
                            <li>✅ 解决常见登录问题</li>
                            <li>✅ 掌握基础配置方法</li>
                            <li>✅ 学会实际使用技巧</li>
                        </ul>
                    </div>
                    <div class="card">
                        <h2>📋 准备清单</h2>
                        <ul>
                            <li>🟢 Node.js 18+ 版本</li>
                            <li>🟢 稳定的王络环境</li>
                            <li>🟢 Google 账号</li>
                            <li>🟢 番茄工具或全局模式</li>
                            <li>🟢 基础终端操作知识</li>
                        </ul>
                    </div>
                </div>
                <div class="card">
                    <h2>🚀 课程亮点</h2>
                    <div class="two-column">
                        <div>
                            <h3>💎 完整流程</h3>
                            <p>从安装到使用，每一步都有详细说明和实际截图演示</p>
                        </div>
                        <div>
                            <h3>🔧 问题解决</h3>
                            <p>涵盖登录失败、项目ID配置等常见问题的解决方案</p>
                        </div>
                    </div>
                </div>
                <div class="screenshot-hint">
                    📸 截图提示：这页适合作为封面图，展示完整的学习路径
                </div>
            </div>

            <!-- 第2页：环境准备 -->
            <div class="page" id="page-1">
                <div class="two-column">
                    <div class="card">
                        <h2>🟢 Node.js 环境检查</h2>
                        <p>Gemini-CLI 需要 Node.js 18+ 版本支持</p>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
# 检查 Node 版本
node --version

# 检查 npm 版本  
npm --version
                        </div>
                        <div class="tip">
                            💡 如果版本过低，请前往 <strong>nodejs.org</strong> 下载最新版本
                        </div>
                    </div>
                    <div class="card">
                        <h2>🍅 代理环境设置</h2>
                        <h3>方法一：终端代理</h3>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
# Windows 设置代理
set HTTP_PROXY=http://127.0.0.1:7890/
set HTTPS_PROXY=http://127.0.0.1:7890/
                        </div>
                        <h3>方法二：全局 Tun 模式（推荐）</h3>
                        <p>在 Clash 等工具中开启全局 Tun 模式</p>
                    </div>
                </div>
                
                <div class="card">
                    <h2>🌐 王络连通性测试</h2>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
# 测试能否访问谷歌
ping google.com
                    </div>
                    <div class="image-container">
                        <img src="img/PixPin_2025-06-26_16-36-22.png" alt="ping测试成功截图" />
                        <p>✅ 出现这样的回复说明王络环境正常</p>
                    </div>
                </div>
                
                <div class="screenshot-hint">
                    📸 截图提示：这页展示环境准备步骤，包含ping测试结果
                </div>
            </div>

            <!-- 第3页：安装步骤 -->
            <div class="page" id="page-2">
                <div class="card">
                    <h2>📦 全局安装 Gemini-CLI</h2>
                    <p>使用 npm 包管理器进行全局安装，安装后可在任意目录使用</p>
                    
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
# 全局安装 Gemini-CLI
npm install -g @google/gemini-cli
                    </div>
                    
                    <div class="tip">
                        🔥 安装完成后，为了方便使用，下面统称为 <strong>gc</strong>
                    </div>
                </div>

                <div class="two-column">
                    <div class="card">
                        <h2>✅ 验证安装</h2>
                        <p>安装完成后验证是否成功</p>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
# 验证安装是否成功
gemini --version

# 或者直接运行
gemini
                        </div>
                    </div>
                    <div class="card">
                        <h2>⚡ 安装技巧</h2>
                        <ul>
                            <li>🚀 使用 <code>-g</code> 参数全局安装</li>
                            <li>🔄 如遇网络问题可重试</li>
                            <li>📝 Windows 可能需要管理员权限</li>
                            <li>🍎 Mac 用户可能需要 sudo 权限</li>
                        </ul>
                    </div>
                </div>

                <div class="warning">
                    ⚠️ <strong>注意事项：</strong><br>
                    • 确保王络环境稳定<br>
                    • Node.js 版本必须 18+<br>
                    • 安装过程可能需要几分钟时间
                </div>

                <div class="screenshot-hint">
                    📸 截图提示：展示安装命令和成功验证结果
                </div>
            </div>

            <!-- 第4页：登录流程 -->
            <div class="page" id="page-3">
                <div class="card">
                    <h2>🔐 启动登录流程</h2>
                    <p>安装完成后，输入 <code>gemini</code> 命令进入终端界面</p>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
gemini
                    </div>
                </div>

                <div class="two-column">
                    <div class="card">
                        <h2>📱 进入登录界面</h2>
                        <div class="image-container">
                            <img src="img/PixPin_2025-06-26_16-24-06.png" alt="输入gemini命令" />
                            <p>输入 gemini 进入终端界面</p>
                        </div>
                    </div>
                    <div class="card">
                        <h2>🌐 选择 Google 账号</h2>
                        <div class="image-container">
                            <img src="img/PixPin_2025-06-26_16-37-24.png" alt="选择Google账号" />
                            <p>在弹出页面选择要登录的账号</p>
                        </div>
                    </div>
                </div>

                <div class="two-column">
                    <div class="card">
                        <h2>✅ 确认登录</h2>
                        <div class="image-container">
                            <img src="img/PixPin_2025-06-26_16-37-46.png" alt="点击登录按钮" />
                            <p>点击登录按钮完成授权</p>
                        </div>
                    </div>
                    <div class="card">
                        <h2>🎉 登录成功</h2>
                        <div class="image-container">
                            <img src="img/PixPin_2025-06-26_16-38-05.png" alt="登录成功界面" />
                            <p>看到此界面说明登录成功！</p>
                        </div>
                    </div>
                </div>

                <div class="tip">
                    💡 <strong>登录成功标志：</strong><br>
                    • 浏览器显示"登录成功"页面<br>
                    • 终端返回到 Gemini 命令行界面<br>
                    • 可以开始正常使用 AI 对话功能
                </div>

                <div class="screenshot-hint">
                    📸 截图提示：展示完整登录流程的4个关键截图
                </div>
            </div>

            <!-- 第5页：问题解决 -->
            <div class="page" id="page-4">
                <div class="card">
                    <h2>❌ 常见登录错误</h2>
                    <div class="warning">
                        <strong>错误信息：</strong><br>
                        Failed to login. Workspace accounts and licensed Code Assist users must configure GOOGLE_CLOUD_PROJECT
                    </div>
                    <div class="image-container">
                        <img src="img/PixPin_2025-06-26_16-39-31.png" alt="登录错误截图" />
                        <p>出现这个错误不用慌，下面教你解决</p>
                    </div>
                </div>

                <div class="two-column">
                    <div class="card">
                        <h2>🔑 获取项目 ID</h2>
                        <h3>步骤1：访问 AI Studio</h3>
                        <p>打开链接：<strong>https://aistudio.google.com</strong></p>
                        <div class="image-container">
                            <img src="img/PixPin_2025-06-26_16-42-15.png" alt="AI Studio首页" />
                        </div>
                        
                        <h3>步骤2：点击项目编号</h3>
                        <div class="image-container">
                            <img src="img/PixPin_2025-06-26_16-42-32.png" alt="点击项目编号" />
                        </div>
                    </div>
                    <div class="card">
                        <h2>📋 复制项目 ID</h2>
                        <h3>步骤3：复制纯数字 ID</h3>
                        <div class="image-container">
                            <img src="img/PixPin_2025-06-26_16-42-46.png" alt="复制项目ID" />
                            <p>复制类似 <code>599***</code> 的纯数字项目编号</p>
                        </div>
                        
                        <div class="tip">
                            🎯 <strong>重要提醒：</strong><br>
                            只需复制纯数字的项目编号，不要复制项目名称
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2>⚙️ 启用 Cloud AI Companion API</h2>
                    <p>访问：<strong>https://console.developers.google.com/apis/api/cloudaicompanion.googleapis.com/overview</strong></p>
                    <div class="image-container">
                        <img src="img/PixPin_2025-06-26_16-56-47.png" alt="启用API" />
                        <p>确保 API 处于启用状态</p>
                    </div>
                </div>

                <div class="screenshot-hint">
                    📸 截图提示：展示问题解决的关键步骤和API启用界面
                </div>
            </div>

            <!-- 第6页：配置设置 -->
            <div class="page" id="page-5">
                <div class="card">
                    <h2>📁 创建环境变量文件</h2>
                    <h3>Windows 用户路径：</h3>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
%USERPROFILE%/.gemini
                    </div>
                    <p>在上述文件夹中新建 <code>.env</code> 文件</p>
                </div>

                <div class="two-column">
                    <div class="card">
                        <h2>✏️ 编辑 .env 文件</h2>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
# 替换成你自己的项目ID
export GOOGLE_CLOUD_PROJECT=599***
                        </div>
                        <div class="image-container">
                            <img src="img/PixPin_2025-06-26_16-45-45.png" alt="env文件配置" />
                            <p>创建 .env 文件并写入项目ID</p>
                        </div>
                    </div>
                    <div class="card">
                        <h2>🔄 重启验证</h2>
                        <p>保存文件后，重启终端并重新进入 gemini</p>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
# 重新启动
gemini
                        </div>
                        <div class="image-container">
                            <img src="img/PixPin_2025-06-26_16-47-51.png" alt="配置成功界面" />
                            <p>✅ 看到这个界面说明配置成功！</p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2>⚙️ settings.json 高级配置</h2>
                    <p>Gemini CLI 支持通过 <code>settings.json</code> 进行个性化配置</p>
                    
                    <h3>📍 配置文件位置：</h3>
                    <ul>
                        <li><strong>用户设置：</strong> <code>~/.gemini/settings.json</code></li>
                        <li><strong>项目设置：</strong> <code>.gemini/settings.json</code>（项目根目录）</li>
                    </ul>

                    <h3>🔌 MCP 服务器配置示例：</h3>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": [
        "mcp-feedback-enhanced@latest"
      ],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false"
      },
      "autoApprove": [
        "interactive_feedback"
      ]
    }
  }
}
                    </div>
                </div>

                <div class="screenshot-hint">
                    📸 截图提示：展示配置文件创建和成功启动界面
                </div>
            </div>

            <!-- 第7页：使用技巧 -->
            <div class="page" id="page-6">
                <div class="two-column">
                    <div class="card">
                        <h2>⚡ 非交互模式</h2>
                        <h3>管道输入模式：</h3>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
echo "煎饼果子好吃吗😋？" | gemini
                        </div>

                        <h3>参数模式：</h3>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
gemini -p "煎饼果子好吃吗😋？"
                        </div>

                        <div class="tip">
                            💡 非交互模式适合脚本调用和快速查询
                        </div>
                    </div>
                    <div class="card">
                        <h2>🔧 IDE 集成使用</h2>
                        <p>在 Cursor、VSCode 等编辑器中打开终端：</p>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
gemini
                        </div>
                        <p>默认会在当前项目目录下运行，AI 可以：</p>
                        <ul>
                            <li>🔍 分析项目结构</li>
                            <li>📝 检查 README.md</li>
                            <li>🐛 协助调试代码</li>
                            <li>📚 解释项目功能</li>
                        </ul>
                    </div>
                </div>

                <div class="card">
                    <h2>🚀 实际使用演示</h2>
                    <div class="image-container">
                        <img src="img/PixPin_2025-06-26_23-19-53.png" alt="项目分析示例" />
                        <p>输入"检查我这个项目是做什么的？"，AI 会自动分析项目</p>
                    </div>
                    
                    <div class="warning">
                        ⚠️ <strong>局限性说明：</strong><br>
                        目前 Gemini-CLI 只能逐个文件分析，缺少像 Cursor 那样的项目整体向量化能力，但依然是很好的 AI 编程助手！
                    </div>
                </div>

                <div class="two-column">
                    <div class="card">
                        <h2>💡 使用技巧</h2>
                        <ul>
                            <li>🌐 确保王络环境稳定</li>
                            <li>🔄 如需代理，每次重启终端需重新设置</li>
                            <li>📁 在项目根目录使用效果更佳</li>
                            <li>❓ 问题要具体明确，便于 AI 理解</li>
                        </ul>
                    </div>
                    <div class="card">
                        <h2>🎯 最佳实践</h2>
                        <ul>
                            <li>📋 使用项目级 settings.json 配置</li>
                            <li>🔧 配置常用的 MCP 服务器</li>
                            <li>💾 定期备份重要配置文件</li>
                            <li>🚀 结合 IDE 使用体验更佳</li>
                        </ul>
                    </div>
                </div>

                <div class="screenshot-hint">
                    📸 截图提示：展示实际使用场景和项目分析效果
                </div>
            </div>

            <!-- 第8页：总结收藏 -->
            <div class="page" id="page-7">
                <div class="card">
                    <h2>📚 完整操作清单</h2>
                    <div class="two-column">
                        <div>
                            <h3>✅ 安装步骤</h3>
                            <ol>
                                <li>检查 Node.js 18+ 环境</li>
                                <li>配置王络代理或 Tun 模式</li>
                                <li>运行安装命令</li>
                                <li>验证安装成功</li>
                            </ol>
                        </div>
                        <div>
                            <h3>✅ 登录配置</h3>
                            <ol>
                                <li>运行 gemini 命令</li>
                                <li>完成 Google 账号登录</li>
                                <li>获取项目 ID</li>
                                <li>配置环境变量</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="two-column">
                    <div class="card">
                        <h2>🔗 重要链接汇总</h2>
                        <div class="tip">
                            <strong>🌐 Node.js 官网：</strong><br>
                            https://nodejs.org<br><br>
                            <strong>🤖 Google AI Studio：</strong><br>
                            https://aistudio.google.com<br><br>
                            <strong>☁️ Google Cloud Console：</strong><br>
                            https://console.developers.google.com<br><br>
                            <strong>📖 官方文档：</strong><br>
                            https://goo.gle/gemini-cli-auth-docs
                        </div>
                    </div>
                    <div class="card">
                        <h2>🎯 关键命令速查</h2>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
# 安装命令
npm install -g @google/gemini-cli

# 启动 Gemini
gemini

# 非交互模式
echo "问题" | gemini
gemini -p "问题"

# 检查版本
gemini --version
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2>🔧 故障排除快速指南</h2>
                    <div class="two-column">
                        <div>
                            <h3>❌ 登录失败</h3>
                            <ul>
                                <li>检查王络环境</li>
                                <li>获取并配置项目 ID</li>
                                <li>启用 Cloud AI API</li>
                                <li>重启终端重新登录</li>
                            </ul>
                        </div>
                        <div>
                            <h3>⚠️ 命令无效</h3>
                            <ul>
                                <li>确认 Node.js 版本 18+</li>
                                <li>检查全局安装是否成功</li>
                                <li>重新运行安装命令</li>
                                <li>检查系统环境变量</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2>🌟 学习成果</h2>
                    <p>恭喜你！完成本教程后，你已经掌握：</p>
                    <div class="two-column">
                        <div>
                            <ul>
                                <li>🎯 Gemini-CLI 完整安装流程</li>
                                <li>🔐 Google 账号登录认证</li>
                                <li>🔧 常见问题解决方案</li>
                                <li>⚙️ 个性化配置方法</li>
                            </ul>
                        </div>
                        <div>
                            <ul>
                                <li>💡 多种使用模式</li>
                                <li>🚀 IDE 集成技巧</li>
                                <li>📋 最佳实践方法</li>
                                <li>🔍 故障排除能力</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="tip">
                    🎉 <strong>教程完成！</strong><br>
                    现在你可以开始享受 AI 编程助手带来的便利了！<br>
                    记得收藏本教程，随时查阅关键步骤。
                </div>

                <div class="screenshot-hint">
                    📸 截图提示：这页包含完整的参考资料，适合收藏使用
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🌟 小红书技术教程 | 持续更新中 | 关注不迷路 🌟</p>
            <p>📱 适合截图发布到小红书 | 每页独立完整 | 便于分享传播</p>
        </div>
    </div>

    <script>
        let currentPage = 0;
        const totalPages = 8;

        function showPage(pageIndex) {
            // 隐藏所有页面
            for (let i = 0; i < totalPages; i++) {
                document.getElementById(`page-${i}`).classList.remove('active');
                document.querySelectorAll('.nav-btn')[i].classList.remove('active');
            }
            
            // 显示指定页面
            document.getElementById(`page-${pageIndex}`).classList.add('active');
            document.querySelectorAll('.nav-btn')[pageIndex].classList.add('active');
            currentPage = pageIndex;
        }

        function copyCode(button) {
            const codeBlock = button.parentElement;
            const codeText = codeBlock.textContent.replace('复制', '').trim();
            
            navigator.clipboard.writeText(codeText).then(() => {
                button.textContent = '已复制';
                setTimeout(() => {
                    button.textContent = '复制';
                }, 2000);
            });
        }

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft' && currentPage > 0) {
                showPage(currentPage - 1);
            } else if (e.key === 'ArrowRight' && currentPage < totalPages - 1) {
                showPage(currentPage + 1);
            }
        });
    </script>
</body>
</html> 