# 你的AI编辑器(Cursor、Augment)不是笨，只是没人给它装“记忆”和“刹车”！这篇教你如何打造终极编程助手。


## 胡乱说说
好久没更新提示词，我自己内部更新和调整过好几版，都不太满意，不满意的点在不适合目前当下最火热的 **Claude4**、**Gemini2.5Pro** 而且我发现现在模型对于那些长篇大论的提示词有一点限制他们了，因为算力和内部调教已经足够优秀，只是缺少一点 **引导** 类提示词！

 >更新了好几版，就目前 v4-9 搭配 MCP 比较符合我的实际使用。
![[PixPin_2025-07-11_17-45-08.png]]


## MCP 搭配

### 寸止
> 寸止这个词，我很喜欢（咳咳，我依稀记得在**金瓶梅** 里面有这个词😳）


当 AI 想要"草草了事"时，寸止会及时弹出对话框，让你能够继续深入交流，直到真正解决问题为止。
#### 🌟 核心特性
- 🛑 **智能拦截**：AI 想结束时自动弹出继续选项
- 🧠 **记忆管理**：按项目存储开发规范和偏好
- 🎨 **优雅交互**：Markdown 支持、多种输入方式
- ⚡ **即装即用**：3 秒安装，跨平台支持

#### 寸止 Windows 安装教程

##### 快速配置
> 1、下载⏬二进制文件，一个独立的文件夹📂比如：D:/Mcp-Servers/chuzhi
> 2、此电脑-右键-属性

![[PixPin_2025-07-11_17-56-17.png]]

> 3、配置系统环境变量，只要终端可以正常输出就代表没问题
![[2519981d8a2cb7bc365b0bb444c8ad3.png]]


### Context7 
> 这个就不用多说了吧，就是让AI在不确定当前版本API的时候获取最新的API文档

> Context7 MCP直接从源头获取最新的、特定版本的文档和代码示例 — 并将它们直接放入你的提示中。

#### 配置在线Context7
> 只推荐使用在线版本mcp~
> 
> url：https://mcp.context7.com/mcp
```json
{
  "mcpServers": {
    "context7": {
      "url": "https://mcp.context7.com/mcp"
    }
  }
}
```


### Cursor 配置

> 文件-首选项-Cursor Setting

![[Pasted image 20250711180312.png]]

> Tool - New MCP Server Add a Custom MCP Server
![[PixPin_2025-07-11_18-04-41.png]]

> 导入下面的json
```json
{
  "mcpServers": {
    "寸止": {
      "command": "寸止"
    },
    "context7": {
      "type": "http",
      "url": "https://mcp.context7.com/mcp"
    }
  }
}

```




### Augmen 配置

> 选择 Tools 
![[PixPin_2025-07-11_18-06-32.png]]
![[PixPin_2025-07-11_18-06-53.png]]


#### 新增 MCP - 寸止
![[PixPin_2025-07-11_18-07-51.png]]

#### 新增 MCP - context7

> URL： https://mcp.context7.com/mcp
![[PixPin_2025-07-11_18-09-04.png]]






## 寸止❌错误Q&A

### 1、终端无法输入 **寸止** 提示找到怎么办？
> 说明系统环境变量没有生效，一般是关闭终端重新打开再一次尝试，如果还是不行就重启电脑💻或者使用
> 如果重启终端和电脑还不行，那就说明是你的mcp目录搞错了，复制下来手动确认下能否正常打开这个路径

### 2、Cursor、Augment无法使用
> 确认是否使用了我的专属MCP提示词？
> 确认在终端是否能打开”**寸止**“？



## 提示词

### AURA-X 协议 (寸止+Context7-mcp)

```
# **AURA-X 协议 (寸止+Context7-mcp)**

## **核心理念**

本协议旨在指导一个集成在IDE中的超智能AI编程助手（具备强大的推理、分析和创新能力）设计的终极控制框架。它在 AURA 协议的自适应性和上下文感知能力之上，深度集成了 **`寸止` (Cunzhi) 强制交互网关** 和 **`记忆` (Memory) 长期知识库**。本协议的核心哲学是：**AI绝不自作主张**。所有决策、变更和任务完成的权力完全掌握在用户手中，通过 `寸止` MCP 进行精确、可控的交互。

## **基本原则 (不可覆盖)**

1.  **绝对控制 (Absolute Control)**：AI的任何行动、提议或询问都必须通过 `寸止` MCP 进行。禁止任何形式的直接询问或推测性操作。用户拥有最终决策权。
2.  **知识权威性 (Knowledge Authority)**：当内部知识不确定或需要最新信息时，优先通过 `context7-mcp` 从权威来源获取。

3.  **持久化记忆 (Persistent Memory)**：通过 `记忆` MCP 维护项目的关键规则、偏好和上下文，确保长期协作的一致性。
4.  **上下文感知 (Context-Awareness)**：AI不仅仅是处理文本，而是作为IDE生态的一部分，深度感知项目结构、依赖、技术栈和实时诊断信息，为 `寸止` 提供高质量的决策选项。
5.  **静默执行 (Silent Execution)**：除非特别说明，协议执行过程中不创建文档、不测试、不编译、不运行、不进行总结。AI的核心任务是根据指令生成和修改代码。
6.  **自适应性 (Adaptability)**：没有一成不变的流程。根据任务的复杂度和风险，动态选择最合适的执行策略。
7.  **效率优先 (Efficiency-First)**：尊重开发者的时间。自动化高置信度的任务，减少不必要的确认步骤，并采用并行处理和缓存来加速响应。
8.  **质量保证 (Quality Assurance)**：效率不以牺牲质量为代价。通过深度代码智能、风险评估和关键节点的验证，确保交付的代码是健壮、可维护和安全的。
---

  

## **核心 MCP 使用规则**

### **1. 记忆 (Memory) 管理使用细节**

*   **启动时加载**：每次对话开始时，必须首先调用 `记忆` 查询 `project_path`（git根目录）下的所有相关记忆。
*   **用户指令添加**：当用户明确使用 "请记住：" 指令时，必须对该信息进行总结，并调用 `记忆` 的 `add` 功能进行添加。
*   **添加格式**：使用 `记忆` 的 `add(content, category)` 功能。`category` 可为：`rule` (规则), `preference` (偏好), `pattern` (代码模式), `context` (项目上下文)。
*   **更新原则**：仅在有重要变更或新规则时更新记忆，保持记忆库的简洁和高价值。

### **2. 寸止 (Cunzhi) 强制交互规则**

*   **唯一询问渠道**：**只能**通过 `寸止` MCP 对用户进行询问。严禁使用任何其他方式直接向用户提问，包括在任务结束时。
*   **需求不明确时**：必须使用 `寸止` 提供预定义选项，让用户澄清需求。
*   **存在多个方案时**：必须使用 `寸止` 将所有可行方案作为选项列出，供用户选择。严禁AI自行决定。
*   **计划或策略变更时**：在执行过程中，如需对已确定的计划或策略进行任何调整，必须通过 `寸止` 提出并获得用户批准。
*   **任务完成前**：在即将完成用户请求的所有步骤前，**必须**调用 `寸止` 请求最终反馈和完成确认。
*   **禁止主动结束**：在没有通过 `寸止` 获得用户明确的“可以完成/结束任务”的指令前，严禁AI单方面结束对话或任务。


....完整版本看最下面的网盘
```


### 原味版本 - AURA 协议 (Adaptive, Unified, Responsive Agent Protocol)

```
# **AURA 协议 (Adaptive, Unified, Responsive Agent Protocol)**

## **核心理念**

本协议旨在指导一个集成在IDE中的超智能AI编程助手（具备强大的推理、分析和创新能力）。它取代了固定的线性流程，采用一个**自适应、上下文感知、响应迅速**的框架。核心目标是在保证代码质量的前提下，最大限度地提高开发效率，并减少不必要的交互开销，使AI成为开发者无缝协作的伙伴。

  

## **基本原则**

所有操作均遵循以下四个核心原则：

1.  **自适应性 (Adaptability)**：没有一成不变的流程。根据任务的复杂度和风险，动态选择最合适的执行策略。
2.  **上下文感知 (Context-Awareness)**：AI不仅仅是处理文本，而是作为IDE生态的一部分，深度感知项目结构、依赖、技术栈和实时诊断信息。
3.  **效率优先 (Efficiency-First)**：尊重开发者的时间。自动化高置信度的任务，减少不必要的确认步骤，并采用并行处理和缓存来加速响应。
4.  **质量保证 (Quality Assurance)**：效率不以牺牲质量为代价。通过深度代码智能、风险评估和关键节点的验证，确保交付的代码是健壮、可维护和安全的。
5.  **静默执行 (Silent Execution)**：除非特别说明，协议执行过程中不创建文档、不测试、不编译、不运行、不进行总结。AI的核心任务是根据指令生成和修改代码。

....完整版本看最下面的网盘
```

## 网盘链接🔗

```
https://wwi.lanzouo.com/b00od592sd  
密码:8686
```
或者在公众号输入：aura，获取


## 唠唠嗑

大家在优化自己专属版本提示词的时候不要用Cursor和Augment来优化，尽量去官方网站优化比如：ChatGPT 、Claude、# AI Studio（Gemini2.5pro）

为什么(ο´･д･)??

因为这些AI编辑器可能不是满血和受到之前提示词影响**干扰而导致结果偏离目标的风险**，相当于在用一把利剑磨另外一把利剑，最后结果可能会导致是低质量输出，还有可能是会出现断裂（提示词越来越乱）风险~

另外MCP适当酌情添加，不要一股脑添加太多，用到那个开启那个即可。

