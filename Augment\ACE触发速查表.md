# ACE触发速查表

## 🚀 快速触发方法

### 1. 模式切换
```
聊天界面 → 切换到"Agent模式" → 输入查询
```

### 2. 关键词触发
```
架构分析：整体架构、系统设计、模块关系
代码库级：在整个项目中、所有相关文件、项目范围内
深度分析：深入分析、详细解释、完整理解
```

### 3. 文件引用
```
@文件名.java - 引用特定文件
@*Service.java - 引用所有Service文件
@application.yml - 引用配置文件
```

## 💡 高效提示词模板

### 代码理解
```
请深入分析[功能/模块]的实现：
1. 核心逻辑和算法
2. 与其他模块的交互
3. 在整个项目中的作用
4. 潜在的改进点
```

### 问题诊断
```
帮我诊断这个问题：[具体问题]
请检查：
- 相关的代码文件
- 可能的错误原因
- 类似的已知问题
- 建议的解决方案
```

### 架构分析
```
从整体架构角度分析[功能/模块]：
- 在系统中的位置
- 与其他组件的关系
- 数据流向和处理过程
- 设计模式的使用
```

### 性能分析
```
分析[功能/模块]的性能问题：
- 识别性能瓶颈
- 检查相关配置
- 对比最佳实践
- 提供优化建议
```

## ⚡ 优化技巧

### DO ✅
- 具体明确的问题
- 包含上下文信息
- 指定期望输出格式
- 提及相关文件或模块
- 分层次提问

### DON'T ❌
- 过于宽泛的问题
- 缺乏上下文的查询
- 一次问多个不相关问题
- 没有明确的目标

## 🎯 实战示例

### Spring Boot架构分析
```
"请分析这个Spring Boot项目的整体架构，包括：
1. 各层之间的关系（Controller、Service、Repository）
2. 数据流向和处理过程
3. 配置文件的作用
4. 与数据库的交互方式"
```

### 性能问题诊断
```
"这个接口响应很慢，帮我分析：
@GetMapping('/api/users')
请检查相关的Service、Repository和数据库查询"
```

### 安全审计
```
"对这个项目进行安全审计：
- 用户认证和授权机制
- 数据传输和存储安全
- 常见安全漏洞检查"
```

## 📊 触发成功率

### 高成功率（90%+）
- 包含"整个项目"、"所有文件"
- 使用@文件名引用
- Agent模式
- 架构分析查询

### 中等成功率（60-90%）
- 涉及多个文件的问题
- 性能和安全查询
- 代码重构建议

### 低成功率（<60%）
- 简单语法问题
- 单一文件小问题
- 基础概念解释

## 🔧 高级技巧

### 链式查询
```
1. "分析这个项目的整体架构"
2. "在这个架构中，用户服务如何与其他服务交互？"
3. "用户服务的认证机制有什么安全风险？"
```

### 多维度分析
```
"从以下角度分析支付模块：
1. 功能完整性
2. 安全性
3. 性能
4. 可维护性
5. 可扩展性"
```

### 条件触发
```
大型项目：限制分析范围到具体模块
微服务：关注服务间通信和依赖
遗留系统：识别技术债务和重构建议
```

